<?php

/**
 * <PERSON>ript để test API getDayCheckinCheckoutDetails
 * Usage: php test_api.php
 */

// Cấu hình
$baseUrl = 'http://localhost:8080'; // Thay đổi theo URL của bạn
$apiToken = 'YOUR_API_TOKEN'; // Thay đổi token
$userId = 1; // Thay đổi user ID
$date = '2025-07-09'; // Thay đổi ngày test

function makeApiRequest($url, $token) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $token,
        'Accept: application/json',
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    if ($error) {
        return ['error' => $error];
    }
    
    return [
        'http_code' => $httpCode,
        'response' => json_decode($response, true)
    ];
}

echo "=== Testing API: getDayCheckinCheckoutDetails ===\n";
echo "Base URL: {$baseUrl}\n";
echo "Date: {$date}\n\n";

// Test API
$url = "{$baseUrl}/api/v1/attendance-explanations/day-checkin-checkout-details/{$date}";
$result = makeApiRequest($url, $apiToken);

if (isset($result['error'])) {
    echo "CURL Error: " . $result['error'] . "\n";
    exit(1);
}

echo "HTTP Code: " . $result['http_code'] . "\n";

if ($result['http_code'] === 200) {
    echo "✅ API Success!\n\n";
    
    $data = $result['response']['data'] ?? [];
    $records = $data['records'] ?? [];
    
    echo "Total Records: " . count($records) . "\n";
    echo "Date: " . ($data['date'] ?? 'N/A') . "\n\n";
    
    foreach ($records as $index => $record) {
        echo "Record #" . ($index + 1) . ":\n";
        echo "  ID: " . $record['id'] . "\n";
        echo "  Type: " . $record['type'] . "\n";
        echo "  Checkin: " . ($record['checkin'] ?? 'NULL') . "\n";
        echo "  Checkout: " . ($record['checkout'] ?? 'NULL') . "\n";
        echo "  Shift: " . ($record['shift_name'] ?? 'NULL') . "\n";
        
        if ($record['checkin_company_address']) {
            echo "  ✅ Checkin Address: " . $record['checkin_company_address']['name'] . " (ID: " . $record['checkin_company_address']['id'] . ")\n";
        } else {
            echo "  ❌ Checkin Address: NULL\n";
        }
        
        if ($record['checkout_company_address']) {
            echo "  ✅ Checkout Address: " . $record['checkout_company_address']['name'] . " (ID: " . $record['checkout_company_address']['id'] . ")\n";
        } else {
            echo "  ❌ Checkout Address: NULL\n";
        }
        
        echo "\n";
    }
    
    // Thống kê
    $withCheckinAddress = array_filter($records, function($r) { return !is_null($r['checkin_company_address']); });
    $withCheckoutAddress = array_filter($records, function($r) { return !is_null($r['checkout_company_address']); });
    
    echo "=== Statistics ===\n";
    echo "Records with Checkin Address: " . count($withCheckinAddress) . "/" . count($records) . "\n";
    echo "Records with Checkout Address: " . count($withCheckoutAddress) . "/" . count($records) . "\n";
    
} else {
    echo "❌ API Error!\n";
    echo "Response: " . json_encode($result['response'], JSON_PRETTY_PRINT) . "\n";
}

echo "\n=== Instructions ===\n";
echo "1. Update \$baseUrl, \$apiToken, \$userId, \$date in this script\n";
echo "2. Check Laravel logs: tail -f storage/logs/laravel.log\n";
echo "3. Run debug command: php artisan debug:timesheet-relationships --user_id={$userId} --date={$date}\n";
echo "4. Check database directly if needed\n";
