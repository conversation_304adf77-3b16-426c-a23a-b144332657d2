# Debug Guide: Company Address Issue

## 1. Xem <PERSON>vel Logs

### Cách 1: Xem log file trự<PERSON> ti<PERSON>
```bash
# Xem log realtime
tail -f storage/logs/laravel.log

# Xem log theo ngày hôm nay
tail -f storage/logs/laravel-$(date +%Y-%m-%d).log

# Xem 100 dòng cuối
tail -100 storage/logs/laravel.log

# Lọc log theo keyword
tail -f storage/logs/laravel.log | grep "getDayCheckinCheckoutDetails"
```

### Cách 2: Sử dụng Docker
```bash
# Xem logs container
docker-compose logs -f app

# Exec vào container và xem log
docker-compose exec app tail -f storage/logs/laravel.log

# Hoặc chạy command trong container
docker-compose exec app php artisan debug:timesheet-relationships --user_id=1 --date=2025-07-09
```

## 2. Chạy Debug Command

```bash
# Debug relationships tổng quát
php artisan debug:timesheet-relationships

# Debug cho user và ngày cụ thể
php artisan debug:timesheet-relationships --user_id=1 --date=2025-07-09 --limit=10
```

## 3. Test API

### Cách 1: Sử dụng script PHP
```bash
# Chỉnh sửa file test_api.php với thông tin đúng
# Sau đó chạy:
php test_api.php
```

### Cách 2: Sử dụng curl
```bash
curl -X GET "http://localhost:8080/api/v1/attendance-explanations/day-checkin-checkout-details/2025-07-09" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Accept: application/json"
```

### Cách 3: Sử dụng Postman/Insomnia
- URL: `GET /api/v1/attendance-explanations/day-checkin-checkout-details/{date}`
- Headers: `Authorization: Bearer YOUR_TOKEN`

## 4. Kiểm tra Database

### Kiểm tra foreign keys
```sql
SELECT 
    CONSTRAINT_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM 
    information_schema.KEY_COLUMN_USAGE 
WHERE 
    TABLE_SCHEMA = 'lavian_spa'
    AND TABLE_NAME = 'timesheets' 
    AND REFERENCED_TABLE_NAME IS NOT NULL;
```

### Kiểm tra dữ liệu sample
```sql
SELECT 
    t.id,
    t.user_id,
    t.date,
    t.company_address_id,
    t.checkout_company_address_id,
    ca1.name as checkin_address_name,
    ca2.name as checkout_address_name
FROM timesheets t
LEFT JOIN company_addresses ca1 ON t.company_address_id = ca1.id
LEFT JOIN company_addresses ca2 ON t.checkout_company_address_id = ca2.id
WHERE t.user_id = 1 AND t.date = '2025-07-09'
ORDER BY t.created_at DESC;
```

### Kiểm tra company_addresses table
```sql
SELECT id, name, lat, long FROM company_addresses LIMIT 10;
```

## 5. Các Log Patterns để Tìm

Khi xem logs, hãy tìm các patterns sau:

### Logs thành công:
```
[INFO] === START getDayCheckinCheckoutDetails ===
[INFO] Timesheets query result: {"count":2,"user_id":1,"date":"2025-07-09"}
[INFO] Processing current day timesheet: {"timesheet_id":123,"companyAddress_exists":"yes"}
[INFO] Successfully formatted company address: {"id":1,"name":"Office A"}
[INFO] === END getDayCheckinCheckoutDetails === {"records_with_checkin_address":2}
```

### Logs có vấn đề:
```
[INFO] Processing current day timesheet: {"companyAddress_exists":"no"}
[DEBUG] Company address is null or empty
[ERROR] Error formatting company address: {"error":"..."}
```

## 6. Troubleshooting Steps

### Bước 1: Kiểm tra logs
1. Chạy API call
2. Xem logs realtime: `tail -f storage/logs/laravel.log`
3. Tìm logs với pattern `getDayCheckinCheckoutDetails`

### Bước 2: Kiểm tra database
1. Chạy debug command: `php artisan debug:timesheet-relationships`
2. Kiểm tra foreign keys có tồn tại không
3. Kiểm tra dữ liệu có đúng không

### Bước 3: Kiểm tra relationships
1. Xem log `companyAddress_loaded` có là `true` không
2. Xem log `companyAddress_exists` có là `yes` không
3. Nếu loaded=true nhưng exists=no → dữ liệu bị null
4. Nếu loaded=false → relationship không được load

### Bước 4: Fix issues
- Nếu foreign key thiếu → Chạy migration
- Nếu dữ liệu null → Update dữ liệu
- Nếu relationship không load → Kiểm tra model definition

## 7. Expected Results

### Kết quả mong đợi trong logs:
```
[INFO] === START getDayCheckinCheckoutDetails ===
[INFO] Timesheets query result: {"count":1,"user_id":1,"date":"2025-07-09","previous_count":0,"next_count":0}
[INFO] Processing current day timesheet: {
    "timesheet_id":123,
    "company_address_id":1,
    "checkout_company_address_id":2,
    "companyAddress_loaded":true,
    "checkoutCompanyAddress_loaded":true,
    "companyAddress_exists":"yes",
    "checkoutCompanyAddress_exists":"yes",
    "companyAddress_data":{"id":1,"name":"Office A"},
    "checkoutCompanyAddress_data":{"id":2,"name":"Office B"}
}
[DEBUG] Successfully formatted company address: {"id":1,"name":"Office A","lat":"10.123","long":"106.456"}
[DEBUG] Successfully formatted company address: {"id":2,"name":"Office B","lat":"10.789","long":"106.789"}
[INFO] === END getDayCheckinCheckoutDetails === {
    "total_records":1,
    "records_with_checkin_address":1,
    "records_with_checkout_address":1
}
```

### Kết quả mong đợi trong API response:
```json
{
    "success": true,
    "data": {
        "date": "2025-07-09",
        "records": [
            {
                "id": 123,
                "checkin_company_address": {
                    "id": 1,
                    "name": "Office A",
                    "lat": "10.123",
                    "long": "106.456"
                },
                "checkout_company_address": {
                    "id": 2,
                    "name": "Office B",
                    "lat": "10.789",
                    "long": "106.789"
                }
            }
        ],
        "total_records": 1
    }
}
```
