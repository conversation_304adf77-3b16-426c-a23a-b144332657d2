<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Timesheet;
use App\Models\CompanyAddress;
use Illuminate\Support\Facades\DB;

class DebugTimesheetRelationships extends Command
{
    protected $signature = 'debug:timesheet-relationships {--user_id=} {--date=} {--limit=5}';
    protected $description = 'Debug timesheet relationships with company addresses';

    public function handle()
    {
        $this->info('=== Debugging Timesheet Relationships ===');

        // Kiểm tra foreign keys
        $this->checkForeignKeys();

        // Kiểm tra dữ liệu sample
        $this->checkSampleData();

        // Kiểm tra relationships
        $this->checkRelationships();

        // Test specific user/date nếu có
        if ($this->option('user_id') && $this->option('date')) {
            $this->testSpecificCase();
        }
    }

    private function checkForeignKeys()
    {
        $this->info("\n1. Checking Foreign Keys:");
        
        $foreignKeys = DB::select("
            SELECT 
                CONSTRAINT_NAME,
                COLUMN_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM 
                information_schema.KEY_COLUMN_USAGE 
            WHERE 
                TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'timesheets' 
                AND REFERENCED_TABLE_NAME IS NOT NULL
        ");

        if (empty($foreignKeys)) {
            $this->warn("No foreign keys found for timesheets table!");
        } else {
            foreach ($foreignKeys as $fk) {
                $this->line("- {$fk->CONSTRAINT_NAME}: {$fk->COLUMN_NAME} -> {$fk->REFERENCED_TABLE_NAME}.{$fk->REFERENCED_COLUMN_NAME}");
            }
        }
    }

    private function checkSampleData()
    {
        $this->info("\n2. Checking Sample Data:");
        
        $limit = $this->option('limit');
        
        $timesheets = DB::select("
            SELECT 
                t.id,
                t.user_id,
                t.date,
                t.company_address_id,
                t.checkout_company_address_id,
                ca1.name as checkin_address_name,
                ca2.name as checkout_address_name
            FROM timesheets t
            LEFT JOIN company_addresses ca1 ON t.company_address_id = ca1.id
            LEFT JOIN company_addresses ca2 ON t.checkout_company_address_id = ca2.id
            ORDER BY t.created_at DESC
            LIMIT {$limit}
        ");

        foreach ($timesheets as $ts) {
            $this->line("Timesheet ID: {$ts->id} (User: {$ts->user_id}, Date: {$ts->date})");
            $this->line("  Checkin Address ID: " . ($ts->company_address_id ?? 'NULL') . " -> " . ($ts->checkin_address_name ?? 'NULL'));
            $this->line("  Checkout Address ID: " . ($ts->checkout_company_address_id ?? 'NULL') . " -> " . ($ts->checkout_address_name ?? 'NULL'));
            $this->line("");
        }
    }

    private function checkRelationships()
    {
        $this->info("\n3. Testing Eloquent Relationships:");
        
        $limit = $this->option('limit');
        
        $timesheets = Timesheet::with(['companyAddress', 'checkoutCompanyAddress'])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

        foreach ($timesheets as $timesheet) {
            $this->line("Timesheet ID: {$timesheet->id}");
            $this->line("  Company Address Loaded: " . ($timesheet->relationLoaded('companyAddress') ? 'YES' : 'NO'));
            $this->line("  Checkout Company Address Loaded: " . ($timesheet->relationLoaded('checkoutCompanyAddress') ? 'YES' : 'NO'));
            $this->line("  Company Address Exists: " . ($timesheet->companyAddress ? 'YES' : 'NO'));
            $this->line("  Checkout Company Address Exists: " . ($timesheet->checkoutCompanyAddress ? 'YES' : 'NO'));
            
            if ($timesheet->companyAddress) {
                $this->line("  Checkin Address: {$timesheet->companyAddress->name}");
            }
            
            if ($timesheet->checkoutCompanyAddress) {
                $this->line("  Checkout Address: {$timesheet->checkoutCompanyAddress->name}");
            }
            
            $this->line("");
        }
    }

    private function testSpecificCase()
    {
        $userId = $this->option('user_id');
        $date = $this->option('date');
        
        $this->info("\n4. Testing Specific Case (User: {$userId}, Date: {$date}):");
        
        $timesheets = Timesheet::where('user_id', $userId)
            ->where('date', $date)
            ->with(['companyAddress', 'checkoutCompanyAddress'])
            ->get();

        $this->line("Found {$timesheets->count()} timesheets");
        
        foreach ($timesheets as $timesheet) {
            $this->line("Timesheet ID: {$timesheet->id}");
            $this->line("  Raw company_address_id: {$timesheet->company_address_id}");
            $this->line("  Raw checkout_company_address_id: {$timesheet->checkout_company_address_id}");
            $this->line("  CompanyAddress relationship: " . ($timesheet->companyAddress ? $timesheet->companyAddress->name : 'NULL'));
            $this->line("  CheckoutCompanyAddress relationship: " . ($timesheet->checkoutCompanyAddress ? $timesheet->checkoutCompanyAddress->name : 'NULL'));
        }
    }
}
